import axios from "axios";

export const ROOT_URL = "localhost";
export const API_URL = `http://${ROOT_URL}:8082`;
export const API_URL_IMAGE = `${API_URL}/api/images/`;

axios.defaults.baseURL = API_URL;

export const login = async (email, password) => {
  try {
    const response = await axios.post("/login", { email, password });
    if (response.status === 200 && response.data.token) {
      localStorage.setItem("token", response.data.token);
      localStorage.setItem("accountId", response.data.accountId);
      return response.data;
    }
    throw new Error(response.data.message || "<PERSON>ản hồ<PERSON> không hợp lệ từ server");
  } catch (error) {
    console.error("Login error:", error);
    throw (
      error.response?.data?.message ||
      "Email, mật khẩu không đúng hoặc tài khoản chưa đượ<PERSON> kích hoạt"
    );
  }
};

export const logout = () => {
  localStorage.removeItem("token");
  localStorage.removeItem("accountId");
};
