import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import accountService from "../../services/accountService";

export const fetchAccounts = createAsyncThunk(
  "account/getAccounts",
  async (_, { rejectWithValue }) => {
    try {
      const response = await accountService.fetchAccounts();
      return response.data;
    } catch (err) {
      return rejectWithValue(err.message || "Lỗi tải danh sách tài khoản");
    }
  }
);

export const fetchAccountById = createAsyncThunk(
  "account/getAccountById",
  async (accountId, { rejectWithValue }) => {
    try {
      const response = await accountService.fetchAccountById(accountId);
      return response.data;
    } catch (err) {
      return rejectWithValue(err.message || "Lỗi tải thông tin tài khoản");
    }
  }
);

export const createAccountAdmin = createAsyncThunk(
  "account/createAccountAdmin",
  async (accountRequest, { rejectWithValue }) => {
    try {
      const response = await accountService.createAccountAdmin(accountRequest);
      return response.data;
    } catch (err) {
      return rejectWithValue(err.message || "Lỗi tạo tài khoản admin");
    }
  }
);

export const createAccountUser = createAsyncThunk(
  "account/createAccountUser",
  async (accountRequest, { rejectWithValue }) => {
    try {
      const response = await accountService.createAccountUser(accountRequest);
      return response.data;
    } catch (err) {
      return rejectWithValue(err.message || "Lỗi tạo tài khoản người dùng");
    }
  }
);

export const updateAccount = createAsyncThunk(
  "account/updateAccount",
  async ({ accountId, accountRequest }, { rejectWithValue }) => {
    try {
      const response = await accountService.updateAccount(
        accountId,
        accountRequest
      );
      return response.data;
    } catch (err) {
      return rejectWithValue(err.message || "Lỗi cập nhật tài khoản");
    }
  }
);

export const updateEmail = createAsyncThunk(
  "account/updateEmail",
  async ({ accountId, email }, { rejectWithValue }) => {
    try {
      return (await accountService.updateEmail(accountId, email)).data;
    } catch (err) {
      return rejectWithValue(err.message || "Lỗi cập nhật email");
    }
  }
);

export const updatePassword = createAsyncThunk(
  "account/updatePassword",
  async ({ accountId, password }, { rejectWithValue }) => {
    try {
      return (await accountService.updatePassword(accountId, password)).data;
    } catch (err) {
      return rejectWithValue(err.message || "Lỗi cập nhật mật khẩu");
    }
  }
);

export const deleteAccount = createAsyncThunk(
  "account/deleteAccount",
  async (accountId, { rejectWithValue }) => {
    try {
      return (await accountService.deleteAccount(accountId)).data;
    } catch (err) {
      return rejectWithValue(err.message || "Lỗi xóa tài khoản");
    }
  }
);

const accountSlice = createSlice({
  name: "account",
  initialState: {
    accounts: [],
    account: null,
    err: null,
    loading: false,
  },
  reducers: {
    clearAccountSlice: (state) => {
      state.account = null;
      state.err = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAccounts.pending, (state) => {
        state.loading = true;
        state.err = null;
      })
      .addCase(fetchAccounts.fulfilled, (state, action) => {
        state.accounts = action.payload;
        state.loading = false;
      })
      .addCase(fetchAccounts.rejected, (state, action) => {
        state.loading = false;
        state.err = action.payload;
      })
      .addCase(fetchAccountById.pending, (state) => {
        state.loading = true;
        state.err = null;
      })
      .addCase(fetchAccountById.fulfilled, (state, action) => {
        state.account = action.payload;
        state.loading = false;
      })
      .addCase(fetchAccountById.rejected, (state, action) => {
        state.loading = false;
        state.err = action.payload;
      })
      .addCase(createAccountAdmin.pending, (state) => {
        state.loading = true;
        state.err = null;
      })
      .addCase(createAccountAdmin.fulfilled, (state, action) => {
        state.account = action.payload;
        state.loading = false;
      })
      .addCase(createAccountAdmin.rejected, (state, action) => {
        state.loading = false;
        state.err = action.payload;
      })
      .addCase(createAccountUser.pending, (state) => {
        state.loading = true;
        state.err = null;
      })
      .addCase(createAccountUser.fulfilled, (state, action) => {
        state.account = action.payload;
        state.loading = false;
      })
      .addCase(createAccountUser.rejected, (state, action) => {
        state.loading = false;
        state.err = action.payload;
      })
      .addCase(updateAccount.pending, (state) => {
        state.loading = true;
        state.err = null;
      })
      .addCase(updateAccount.fulfilled, (state, action) => {
        state.account = action.payload;
        state.loading = false;
      })
      .addCase(updateAccount.rejected, (state, action) => {
        state.loading = false;
        state.err = action.payload;
      })
      .addCase(updateEmail.pending, (state) => {
        state.loading = true;
        state.err = null;
      })
      .addCase(updateEmail.fulfilled, (state, action) => {
        state.account = action.payload;
        state.loading = false;
      })
      .addCase(updateEmail.rejected, (state, action) => {
        state.loading = false;
        state.err = action.payload;
      })
      .addCase(updatePassword.pending, (state) => {
        state.loading = true;
        state.err = null;
      })
      .addCase(updatePassword.fulfilled, (state, action) => {
        state.account = action.payload;
        state.loading = false;
      })
      .addCase(updatePassword.rejected, (state, action) => {
        state.loading = false;
        state.err = action.payload;
      })
      .addCase(deleteAccount.pending, (state) => {
        state.loading = true;
        state.err = null;
      })
      .addCase(deleteAccount.fulfilled, (state) => {
        state.account = null;
        state.loading = false;
      })
      .addCase(deleteAccount.rejected, (state, action) => {
        state.loading = false;
        state.err = action.payload;
      });
  },
});

export const { clearAccountSlice } = accountSlice.actions;

export default accountSlice.reducer;
